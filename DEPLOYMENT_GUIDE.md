# 阿里云轻量服务器部署指南

本指南将帮助您将慧通材料管理系统部署到阿里云轻量服务器。

## 准备工作

### 1. 购买阿里云轻量服务器
- 登录阿里云控制台
- 选择轻量应用服务器
- 推荐配置：
  - CPU：2核
  - 内存：4GB
  - 系统盘：60GB SSD
  - 流量包：1000GB/月
- 操作系统：Ubuntu 22.04 LTS

### 2. 域名配置（可选）
- 在阿里云购买域名
- 将域名解析到服务器IP
- 配置SSL证书（可使用阿里云免费SSL证书）

## 部署步骤

### 方法一：一键部署脚本（推荐）

#### 1. 配置服务器环境
```bash
# 在服务器上运行初始化脚本
chmod +x setup-aliyun-server.sh
./setup-aliyun-server.sh
```

#### 2. 本地一键部署

**Linux/Mac:**
```bash
# 修改部署权限
chmod +x deploy-aliyun.sh

# 执行部署（替换为您的服务器IP）
./deploy-aliyun.sh *************** root
```

**Windows:**
```cmd
# 使用批处理脚本部署（需要PuTTY支持）
deploy-aliyun.bat *************** root
```

### 方法二：手动部署

#### 1. 服务器环境配置

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# 重新登录使权限生效
exit
```

#### 2. 上传项目文件

```bash
# 在本地项目目录执行
scp -r * root@YOUR_SERVER_IP:/home/<USER>/huitong-material/
```

#### 3. 服务器端配置

```bash
# 登录服务器
ssh root@YOUR_SERVER_IP

# 进入项目目录
cd /home/<USER>/huitong-material

# 配置环境变量
cp .env.production .env

# 编辑.env文件，设置数据库密码
nano .env

# 启动应用
docker-compose up -d

# 运行数据库迁移
docker-compose exec app npx prisma migrate deploy

# 检查状态
docker-compose ps
```

## 配置文件说明

### 1. 环境变量配置 (.env.production)
```bash
# 生产环境配置
NODE_ENV=production
PORT=3001

# 数据库配置
POSTGRES_USER=huitong_user
POSTGRES_PASSWORD=your_secure_password_here  # 修改为您的密码
POSTGRES_DB=huitong_material
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# 文件上传配置
UPLOAD_DIR=/app/uploads
```

### 2. Docker配置
- **Dockerfile**: 应用容器配置
- **docker-compose.yml**: 服务编排配置
- **nginx.conf**: Nginx反向代理配置

## 高级配置

### 1. 配置HTTPS（推荐）

#### 使用Let's Encrypt免费证书
```bash
# 安装certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取证书（替换为您的域名）
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

#### 手动配置SSL证书
```bash
# 将证书文件上传到服务器
scp your-certificate.crt your-certificate.key root@YOUR_SERVER_IP:/etc/nginx/ssl/

# 修改nginx.conf中的证书路径
sudo nano /etc/nginx/sites-available/huitong-material

# 重启Nginx
sudo systemctl restart nginx
```

### 2. 数据备份

#### 数据库备份脚本
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
DB_NAME="huitong_material"

docker-compose exec -T postgres pg_dump -U huitong_user $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete
```

#### 文件备份
```bash
#!/bin/bash
# backup-files.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /home/<USER>/backups/uploads_backup_$DATE.tar.gz /home/<USER>/huitong-material/uploads/
```

### 3. 监控配置

#### 安装监控工具
```bash
# 安装Node Exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar -xzf node_exporter-1.6.1.linux-amd64.tar.gz
sudo cp node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/
```

## 故障排查

### 1. 检查服务状态
```bash
# 查看容器状态
docker-compose ps

# 查看应用日志
docker-compose logs -f app

# 查看数据库日志
docker-compose logs -f postgres
```

### 2. 常见问题

#### 端口冲突
```bash
# 检查端口占用
sudo netstat -tulnp | grep :3001
sudo netstat -tulnp | grep :5432

# 修改端口（编辑docker-compose.yml）
nano docker-compose.yml
```

#### 权限问题
```bash
# 修复Docker权限
sudo chown -R $USER:$USER /home/<USER>/huitong-material
sudo chmod -R 755 /home/<USER>/huitong-material
```

#### 数据库连接问题
```bash
# 检查数据库连接
docker-compose exec app npx prisma db pull

# 重置数据库
docker-compose down -v
docker-compose up -d
```

## 性能优化

### 1. 系统优化
```bash
# 增加文件描述符限制
echo "fs.file-max = 65535" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 优化Nginx配置
sudo nano /etc/nginx/nginx.conf
```

### 2. 数据库优化
```bash
# 优化PostgreSQL配置
docker-compose exec postgres bash
echo "shared_buffers = 256MB" >> /var/lib/postgresql/data/postgresql.conf
echo "effective_cache_size = 1GB" >> /var/lib/postgresql/data/postgresql.conf
```

## 访问应用

部署完成后，可以通过以下方式访问：

- **IP访问**: http://YOUR_SERVER_IP:3001
- **域名访问**: https://your-domain.com（配置域名后）

## 维护命令

### 日常维护
```bash
# 查看日志
docker-compose logs -f --tail=100

# 重启服务
docker-compose restart

# 更新应用
docker-compose pull
docker-compose up -d

# 备份数据
./backup.sh
```

### 安全更新
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 更新Docker镜像
docker-compose pull
docker-compose up -d

# 清理无用镜像
docker system prune -f
```

## 技术支持

如有问题，请检查：
1. 服务器防火墙设置
2. 安全组配置（阿里云控制台）
3. 域名解析是否正确
4. SSL证书是否有效

联系支持：查看项目README.md获取联系方式