# 🚀 立即部署到阿里云服务器

## 服务器信息
- **IP地址**: ************
- **状态**: 已准备就绪
- **端口**: 3001 (应用) + 5432 (数据库)

## ⚡ 一键部署 (当前目录执行)

### Windows PowerShell 用户
```powershell
# 确保在huitong-material目录下
.\deploy-************.bat
```

### Windows CMD 用户
```cmd
# 确保在huitong-material目录下
deploy-************.bat
```

### Linux/macOS 用户
```bash
# 确保在huitong-material目录下
chmod +x deploy-************.sh
./deploy-************.sh
```

## 🎯 快速验证部署

部署完成后，访问以下地址：
- **应用首页**: http://************:3001
- **健康检查**: http://************:3001/health

## 📋 部署前检查清单

- [ ] 服务器************已开机
- [ ] 已配置SSH密钥认证 (推荐)
- [ ] 当前目录包含所有必要文件
- [ ] Docker Desktop已安装并运行 (Windows用户)

## 🔧 如果部署遇到问题

### 1. SSH连接问题
```bash
# 测试连接
ssh root@************
```

### 2. 手动部署备用方案
```bash
# 上传文件到服务器
scp docker-compose.yml root@************:/root/huitong-material/
scp .env.production root@************:/root/huitong-material/.env

# 连接服务器部署
ssh root@************
cd /root/huitong-material
docker-compose up -d
```

### 3. 获取帮助
查看详细文档：`QUICK_DEPLOY_************.md`

## 🎉 部署成功确认

当看到以下提示时，表示部署完成：
- ✅ "部署成功！"
- ✅ "服务运行正常"
- ✅ 浏览器能访问 http://************:3001

**预计部署时间**: 5-10分钟

**现在就开始部署吧！** 🚀