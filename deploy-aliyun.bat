@echo off
setlocal enabledelayedexpansion

echo 🚀 开始部署到阿里云轻量服务器...

REM 检查是否提供了服务器IP
if "%1"=="" (
    echo ❌ 请提供服务器的IP地址
    echo 用法: deploy-aliyun.bat <服务器IP> [用户名]
    pause
    exit /b 1
)

set SERVER_IP=%1
set USERNAME=%2
if "%USERNAME%"=="" set USERNAME=root

set APP_NAME=huitong-material
set DOCKER_COMPOSE_FILE=docker-compose.yml
set ENV_FILE=.env.production

REM 检查必要文件
if not exist "%DOCKER_COMPOSE_FILE%" (
    echo ❌ docker-compose.yml 文件不存在
    pause
    exit /b 1
)

if not exist "%ENV_FILE%" (
    echo ❌ .env.production 文件不存在
    pause
    exit /b 1
)

echo 📦 正在打包应用...
echo 📝 请确保您已经配置好SSH密钥认证，否则需要输入密码多次

REM 复制文件到服务器
echo 📤 正在上传文件...
scp "%DOCKER_COMPOSE_FILE%" %USERNAME%@%SERVER_IP%:/home/<USER>/
scp "%ENV_FILE%" %USERNAME%@%SERVER_IP%:/home/<USER>/.env
scp "Dockerfile" %USERNAME%@%SERVER_IP%:/home/<USER>/

if exist "init.sql" (
    scp "init.sql" %USERNAME%@%SERVER_IP%:/home/<USER>/
)

echo 🔧 正在服务器上配置环境...
REM 使用plink执行远程命令（需要PuTTY）
plink %USERNAME%@%SERVER_IP% -batch "
    sudo apt update && sudo apt install -y docker.io docker-compose
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker \$USER
    mkdir -p /home/<USER>/uploads
    cd /home/<USER>
    docker-compose down || true
    docker-compose build
    docker-compose up -d
    sleep 30
    docker-compose exec -T app npx prisma migrate deploy
    docker-compose ps
"

echo 🎉 部署完成！
echo 应用将在几分钟后可以通过 http://%SERVER_IP%:3001 访问
pause