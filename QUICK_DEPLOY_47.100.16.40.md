# 慧通材料管理系统 - 快速部署指南

## 🎯 目标服务器
**IP地址**: ************
**系统**: 阿里云轻量应用服务器 (Ubuntu/CentOS)

## 🚀 一键部署 (推荐)

### Linux/macOS 用户
```bash
# 1. 给脚本执行权限
chmod +x deploy-************.sh

# 2. 一键部署
./deploy-************.sh
```

### Windows 用户
```cmd
# 双击运行或命令行执行
deploy-************.bat
```

## 📋 手动部署步骤

### 1. 服务器初始化 (首次部署)
```bash
# 连接服务器
ssh root@************

# 运行初始化脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/huitong-material/main/setup-aliyun-server.sh | bash
```

### 2. 本地准备和上传
```bash
# 构建镜像
docker-compose build

# 上传文件
scp docker-compose.yml root@************:/root/huitong-material/
scp .env.production root@************:/root/huitong-material/.env
scp Dockerfile root@************:/root/huitong-material/
```

### 3. 服务器端部署
```bash
# 连接服务器
ssh root@************

# 进入项目目录
cd /root/huitong-material

# 启动服务
docker-compose up -d

# 数据库迁移
docker-compose exec app npx prisma migrate deploy

# 检查状态
docker-compose ps
```

## 🔧 配置文件检查清单

部署前请确认以下文件已配置正确：

- [ ] `.env.production` - 生产环境配置
- [ ] `docker-compose.yml` - Docker服务配置
- [ ] `Dockerfile` - 应用镜像配置

### 关键配置项 (.env.production)
```bash
# 数据库配置
DATABASE_URL="******************************************/huitong_material"

# 应用配置
NODE_ENV=production
PORT=3001

# 文件上传
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
```

## 📍 访问地址

- **应用访问**: http://************:3001
- **健康检查**: http://************:3001/health
- **API文档**: http://************:3001/api

## 🔍 验证部署

### 1. 服务状态检查
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 健康检查
curl http://************:3001/health
```

### 2. 功能测试
- [ ] 首页访问正常
- [ ] 文件上传功能正常
- [ ] 数据库连接正常
- [ ] API接口响应正常

## 🛠️ 常用维护命令

### 查看日志
```bash
# 实时日志
docker-compose logs -f

# 错误日志
docker-compose logs --tail=100
```

### 服务管理
```bash
# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建
docker-compose build --no-cache
docker-compose up -d
```

### 数据库操作
```bash
# 进入数据库容器
docker-compose exec db psql -U huitong -d huitong_material

# 备份数据库
docker-compose exec db pg_dump -U huitong huitong_material > backup.sql

# 恢复数据库
docker-compose exec -T db psql -U huitong -d huitong_material < backup.sql
```

## 🔐 安全建议

### 1. 修改默认密码
```bash
# 修改数据库密码
ssh root@************
docker-compose exec db psql -U postgres
ALTER USER huitong WITH PASSWORD 'new_secure_password';
```

### 2. 配置防火墙
```bash
# 开放必要端口
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3001/tcp
ufw enable
```

### 3. 设置定期备份
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec db pg_dump -U huitong huitong_material > /root/backups/backup_$DATE.sql
find /root/backups -name "*.sql" -mtime +7 -delete
EOF
chmod +x backup.sh

# 设置定时任务
echo "0 2 * * * /root/huitong-material/backup.sh" | crontab -
```

## 🚨 故障排查

### 常见问题

#### 1. 连接超时
- 检查服务器安全组规则
- 确认端口3001已开放
- 检查防火墙设置

#### 2. 服务启动失败
```bash
# 查看详细日志
docker-compose logs app

# 检查容器状态
docker ps -a

# 重启服务
docker-compose restart
```

#### 3. 数据库连接失败
```bash
# 检查数据库容器
docker-compose exec db psql -U huitong -d huitong_material -c "SELECT 1"

# 重新运行迁移
docker-compose exec app npx prisma migrate deploy
```

## 📞 技术支持

如遇部署问题，请按以下步骤排查：

1. **检查网络连接**: `ping ************`
2. **检查服务状态**: `curl http://************:3001/health`
3. **查看日志**: `docker-compose logs`
4. **重启服务**: `docker-compose restart`

## 🎯 下一步

部署成功后，您可以：

1. **配置域名**: 将域名解析到 ************
2. **启用HTTPS**: 使用Let's Encrypt证书
3. **配置CDN**: 使用阿里云CDN加速
4. **监控告警**: 设置服务器监控

---

**部署完成时间**: 预计5-10分钟
**维护窗口**: 可随时重启，不影响数据