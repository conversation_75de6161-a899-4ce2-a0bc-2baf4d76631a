#!/bin/bash

# 阿里云轻量服务器部署脚本
# 适用于Ubuntu/Debian系统

set -e

echo "🚀 开始部署到阿里云轻量服务器..."

# 检查是否提供了服务器IP
if [ -z "$1" ]; then
    echo "❌ 请提供服务器的IP地址"
    echo "用法: ./deploy-aliyun.sh <服务器IP> [用户名]"
    exit 1
fi

SERVER_IP=$1
USERNAME=${2:-root}
APP_NAME="huitong-material"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env.production"

# 检查必要文件
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    echo "❌ docker-compose.yml 文件不存在"
    exit 1
fi

if [ ! -f "$ENV_FILE" ]; then
    echo "❌ .env.production 文件不存在"
    exit 1
fi

echo "📦 正在打包应用..."
# 构建Docker镜像
docker-compose -f $DOCKER_COMPOSE_FILE build

echo "📤 正在上传到服务器..."
# 复制必要文件到服务器
scp $DOCKER_COMPOSE_FILE $USERNAME@$SERVER_IP:/home/<USER>/
scp $ENV_FILE $USERNAME@$SERVER_IP:/home/<USER>/.env
scp Dockerfile $USERNAME@$SERVER_IP:/home/<USER>/

# 创建上传目录
ssh $USERNAME@$SERVER_IP "mkdir -p /home/<USER>/uploads"

# 复制数据库初始化脚本
if [ -f "init.sql" ]; then
    scp init.sql $USERNAME@$SERVER_IP:/home/<USER>/
fi

echo "🔧 正在服务器上配置环境..."
# 在服务器上执行命令
ssh $USERNAME@$SERVER_IP << 'EOF'
    # 安装必要的软件
    sudo apt update && sudo apt install -y docker.io docker-compose
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    # 创建必要的目录
    mkdir -p /home/<USER>/uploads
    
    # 停止并移除旧容器
    docker-compose down || true
    
    # 拉取最新的PostgreSQL镜像
    docker pull postgres:15-alpine
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 30
    
    # 运行数据库迁移
    docker-compose exec -T app npx prisma migrate deploy
    
    # 检查服务状态
    docker-compose ps
    
    echo "✅ 部署完成！"
    echo "应用地址: http://$(curl -s ifconfig.me):3001"
EOF

echo "🎉 部署成功！"
echo "应用将在几分钟后可以通过 http://$SERVER_IP:3001 访问"