# Nginx配置文件 - 用于阿里云部署
# 将此文件复制到 /etc/nginx/sites-available/huitong-material
# 然后创建软链接: ln -s /etc/nginx/sites-available/huitong-material /etc/nginx/sites-enabled/

upstream huitong_app {
    server localhost:3001;
    keepalive 32;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL配置（需要您提供证书文件）
    ssl_certificate /etc/nginx/ssl/your-domain.com.crt;
    ssl_certificate_key /etc/nginx/ssl/your-domain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 客户端上传限制
    client_max_body_size 100M;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|glb|gltf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 代理到Node.js应用
    location / {
        proxy_pass http://huitong_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
    }

    # 健康检查
    location /health {
        proxy_pass http://huitong_app/health;
        access_log off;
    }

    # 上传文件路径
    location /uploads/ {
        alias /home/<USER>/huitong-material/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# 默认服务器配置（IP访问）
server {
    listen 80 default_server;
    server_name _;
    
    location / {
        proxy_pass http://huitong_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /health {
        proxy_pass http://huitong_app/health;
        access_log off;
    }
    
    location /uploads/ {
        alias /home/<USER>/huitong-material/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}