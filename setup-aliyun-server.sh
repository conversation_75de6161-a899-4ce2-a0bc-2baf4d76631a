#!/bin/bash

# 阿里云轻量服务器初始化脚本
# 在服务器上运行此脚本进行环境配置

set -e

echo "🔧 开始初始化阿里云服务器环境..."

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要的软件
sudo apt install -y \
    curl \
    wget \
    git \
    unzip \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# 安装Docker
echo "📦 安装Docker..."
sudo apt install -y docker.io
echo "🚀 启动Docker服务..."
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
echo "📦 安装Docker Compose..."
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 安装Nginx（可选，用于反向代理）
echo "📦 安装Nginx..."
sudo apt install -y nginx

# 配置防火墙
echo "🔥 配置防火墙..."
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3001/tcp
sudo ufw --force enable

# 创建应用目录
mkdir -p ~/huitong-material
mkdir -p ~/huitong-material/uploads

# 设置目录权限
chmod 755 ~/huitong-material
chmod 755 ~/huitong-material/uploads

# 创建日志目录
sudo mkdir -p /var/log/huitong-material
sudo chown $USER:$USER /var/log/huitong-material

echo "✅ 服务器初始化完成！"
echo "请重新登录以使docker组权限生效"
echo "下一步: 将项目文件上传到 ~/huitong-material 目录"
echo "然后运行: cd ~/huitong-material && docker-compose up -d"