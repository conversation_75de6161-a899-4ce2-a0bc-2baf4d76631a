version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: huitong-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-huitong_material}
      POSTGRES_USER: ${POSTGRES_USER:-huitong_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_secure_password_here}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - huitong-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-huitong_user} -d ${POSTGRES_DB:-huitong_material}"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: huitong-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3001
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${POSTGRES_DB:-huitong_material}
      POSTGRES_USER: ${POSTGRES_USER:-huitong_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_secure_password_here}
      DATABASE_URL: postgresql://${POSTGRES_USER:-huitong_user}:${POSTGRES_PASSWORD:-your_secure_password_here}@postgres:5432/${POSTGRES_DB:-huitong_material}?schema=public
      UPLOAD_DIR: /app/backend/uploads
    volumes:
      - app_uploads:/app/backend/uploads
    ports:
      - "3001:3001"
    networks:
      - huitong-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  app_uploads:

networks:
  huitong-network:
    driver: bridge