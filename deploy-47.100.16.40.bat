@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo 开始部署慧通材料管理系统到阿里云服务器 ************
echo 部署时间: %date% %time%

set SERVER_IP=************
set USERNAME=root
set PROJECT_NAME=huitong-material

echo 检查必要文件...

if not exist "docker-compose.yml" (
    echo docker-compose.yml 文件不存在
    pause
    exit /b 1
)

if not exist ".env.production" (
    echo .env.production 文件不存在
    pause
    exit /b 1
)

echo 构建Docker镜像...
docker-compose build

if errorlevel 1 (
    echo Docker镜像构建失败
    pause
    exit /b 1
)

echo 上传文件到服务器...
echo 请确保已配置SSH密钥认证，否则需要输入密码

:: 创建服务器端部署脚本
echo Creating server deployment script...
echo #!/bin/bash > deploy_server.sh
echo set -e >> deploy_server.sh
echo echo "开始服务器端部署..." >> deploy_server.sh
echo cd /root/%PROJECT_NAME% >> deploy_server.sh
echo mkdir -p uploads >> deploy_server.sh
echo chmod 755 uploads >> deploy_server.sh
echo echo "停止旧服务..." >> deploy_server.sh
echo docker-compose down ^|^| true >> deploy_server.sh
echo echo "启动新服务..." >> deploy_server.sh
echo docker-compose up -d >> deploy_server.sh
echo echo "等待服务启动..." >> deploy_server.sh
echo sleep 30 >> deploy_server.sh
echo echo "运行数据库迁移..." >> deploy_server.sh
echo docker-compose exec -T app npx prisma migrate deploy >> deploy_server.sh
echo echo "检查服务状态..." >> deploy_server.sh
echo docker-compose ps >> deploy_server.sh
echo echo "健康检查..." >> deploy_server.sh
echo curl -f http://localhost:3001/health ^&^& echo "服务运行正常" ^|^| echo "服务异常" >> deploy_server.sh
echo chmod +x deploy_server.sh

:: 创建远程目录
echo Creating remote directory...
ssh %USERNAME%@%SERVER_IP% "mkdir -p /root/%PROJECT_NAME%" 2>nul
if errorlevel 1 (
    echo SSH连接失败，请检查：
    echo 1. 服务器是否开机
    echo 2. SSH端口(22)是否开放
    echo 3. SSH密钥是否配置正确
    pause
    exit /b 1
)

:: 上传所有必要文件
echo Uploading files...
scp docker-compose.yml %USERNAME%@%SERVER_IP%:/root/%PROJECT_NAME%/
scp .env.production %USERNAME%@%SERVER_IP%:/root/%PROJECT_NAME%/.env
scp Dockerfile %USERNAME%@%SERVER_IP%:/root/%PROJECT_NAME%/
scp deploy_server.sh %USERNAME%@%SERVER_IP%:/root/%PROJECT_NAME%/
if exist "init.sql" scp init.sql %USERNAME%@%SERVER_IP%:/root/%PROJECT_NAME%/

:: 在服务器上执行部署
echo Executing deployment on server...
ssh %USERNAME%@%SERVER_IP% "cd /root/%PROJECT_NAME% && chmod +x deploy_server.sh && ./deploy_server.sh"

:: 清理临时文件
del deploy_server.sh 2>nul

echo.
echo 部署完成！
echo 应用地址: http://%SERVER_IP%:3001
echo 健康检查: http://%SERVER_IP%:3001/health
echo.
echo 后续操作:
echo 1. 配置域名解析 (可选)
echo 2. 配置HTTPS证书 (可选)
echo 3. 设置定期备份
echo.
echo 常用命令:
echo 查看日志: ssh root@%SERVER_IP% "cd /root/%PROJECT_NAME% && docker-compose logs -f"
echo 重启服务: ssh root@%SERVER_IP% "cd /root/%PROJECT_NAME% && docker-compose restart"
echo 停止服务: ssh root@%SERVER_IP% "cd /root/%PROJECT_NAME% && docker-compose down"

pause