# Windows部署脚本修复指南

## 问题原因
终端输出的乱码和命令识别错误主要是由于：
1. Windows批处理文件编码问题（UTF-8 BOM头）
2. 特殊Unicode字符（emoji）在cmd中无法正确显示
3. 中文字符编码不一致

## 修复后的部署方法

### 方法1：使用修复后的脚本
```bash
# 在PowerShell中运行
.\deploy-************.bat

# 在CMD中运行
deploy-************.bat
```

### 方法2：手动部署（推荐）
如果脚本仍有问题，可以使用手动部署：

```bash
# 1. 构建镜像
docker-compose build

# 2. 上传文件（需要安装scp）
scp docker-compose.yml root@************:/root/huitong-material/
scp .env.production root@************:/root/huitong-material/.env
scp Dockerfile root@************:/root/huitong-material/

# 3. SSH到服务器部署
ssh root@************

# 在服务器上执行：
cd /root/huitong-material
mkdir -p uploads
chmod 755 uploads
docker-compose down || true
docker-compose up -d
sleep 30
docker-compose exec -T app npx prisma migrate deploy
docker-compose ps
curl -f http://localhost:3001/health
```

### 方法3：使用PowerShell脚本
创建 `deploy-************.ps1`：

```powershell
# PowerShell部署脚本
$serverIP = "************"
$username = "root"
$projectName = "huitong-material"

Write-Host "开始部署到阿里云服务器 $serverIP" -ForegroundColor Green

# 检查文件
if (!(Test-Path "docker-compose.yml")) {
    Write-Error "docker-compose.yml 文件不存在"
    exit 1
}

if (!(Test-Path ".env.production")) {
    Write-Error ".env.production 文件不存在"
    exit 1
}

# 构建镜像
Write-Host "构建Docker镜像..." -ForegroundColor Yellow
docker-compose build
if ($LASTEXITCODE -ne 0) {
    Write-Error "Docker镜像构建失败"
    exit 1
}

# 创建远程脚本
$remoteScript = @"
#!/bin/bash
set -e
echo "开始服务器端部署..."
cd /root/$projectName
mkdir -p uploads
chmod 755 uploads
echo "停止旧服务..."
docker-compose down || true
echo "启动新服务..."
docker-compose up -d
echo "等待服务启动..."
sleep 30
echo "运行数据库迁移..."
docker-compose exec -T app npx prisma migrate deploy
echo "检查服务状态..."
docker-compose ps
echo "健康检查..."
curl -f http://localhost:3001/health && echo "服务运行正常" || echo "服务异常"
"@

$remoteScript | ssh ${username}@${serverIP} "cat > /root/$projectName/deploy_remote.sh; chmod +x /root/$projectName/deploy_remote.sh"

# 上传文件
Write-Host "上传文件到服务器..." -ForegroundColor Yellow
scp docker-compose.yml "${username}@${serverIP}:/root/$projectName/"
scp .env.production "${username}@${serverIP}:/root/$projectName/.env"
scp Dockerfile "${username}@${serverIP}:/root/$projectName/"

# 执行远程部署
Write-Host "执行远程部署..." -ForegroundColor Yellow
ssh "${username}@${serverIP}" "cd /root/$projectName && ./deploy_remote.sh"

Write-Host "部署完成！" -ForegroundColor Green
Write-Host "应用地址: http://$serverIP:3001"
Write-Host "健康检查: http://$serverIP:3001/health"
```

运行PowerShell脚本：
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\deploy-************.ps1
```

## 验证部署成功

部署完成后，访问以下地址验证：
- 应用主页: http://************:3001
- 健康检查: http://************:3001/health
- API测试: http://************:3001/api/materials

## 常见问题

### SSH连接问题
```bash
# 测试SSH连接
ssh root@************

# 如果连接失败，检查：
# 1. 服务器安全组是否开放22端口
# 2. SSH服务是否运行
# 3. 密码是否正确
```

### Docker问题
```bash
# 在服务器上检查Docker状态
ssh root@************ "docker --version && docker-compose --version"

# 如果未安装，运行：
ssh root@************ "curl -fsSL https://get.docker.com | sh && systemctl start docker && systemctl enable docker"
```

## 一键部署（最简单）
如果上述方法都失败，使用这个最简化的部署：

```bash
# 1. 直接复制粘贴到PowerShell/CMD
ssh root@************ "mkdir -p /root/huitong-material && cd /root/huitong-material"

# 2. 然后逐个上传文件
# 3. 最后在服务器上手动运行docker-compose
```