# 本地开发环境的Docker配置覆盖
# 使用命令: docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d

version: '3.8'

services:
  postgres:
    ports:
      - "5432:5432"  # 暴露数据库端口到本地
    environment:
      POSTGRES_PASSWORD: development_password  # 开发环境密码
    volumes:
      - ./postgres_dev_data:/var/lib/postgresql/data

  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./backend/uploads:/app/backend/uploads  # 本地挂载上传目录
      - ./src:/app/src  # 开发时挂载源码
    environment:
      NODE_ENV: development
      POSTGRES_PASSWORD: development_password
    ports:
      - "3001:3001"
    command: npm run dev:safe  # 开发模式启动

volumes:
  postgres_dev_data: