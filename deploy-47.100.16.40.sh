#!/bin/bash

# 针对阿里云服务器 ************ 的专用部署脚本
# 慧通材料管理系统部署脚本

set -e

SERVER_IP="************"
USERNAME="root"
PROJECT_NAME="huitong-material"

echo "🚀 开始部署慧通材料管理系统到阿里云服务器 $SERVER_IP..."
echo "📋 部署时间: $(date)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查必要文件
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ docker-compose.yml 文件不存在${NC}"
    exit 1
fi

if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ .env.production 文件不存在${NC}"
    exit 1
fi

# 检查SSH连接
echo "🔍 检查SSH连接..."
if ! ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no $USERNAME@$SERVER_IP "echo 'SSH连接成功'" 2>/dev/null; then
    echo -e "${RED}❌ 无法连接到服务器 $SERVER_IP${NC}"
    echo "请确保:"
    echo "1. 服务器已开机"
    echo "2. SSH端口(22)已开放"
    echo "3. 您有正确的SSH密钥或密码"
    exit 1
fi

echo -e "${GREEN}✅ SSH连接正常${NC}"

# 构建本地镜像
echo "📦 构建Docker镜像..."
docker-compose build

# 复制文件到服务器
echo "📤 上传文件到服务器..."
ssh $USERNAME@$SERVER_IP "mkdir -p /root/$PROJECT_NAME"

# 上传必要文件
scp docker-compose.yml $USERNAME@$SERVER_IP:/root/$PROJECT_NAME/
scp .env.production $USERNAME@$SERVER_IP:/root/$PROJECT_NAME/.env
scp Dockerfile $USERNAME@$SERVER_IP:/root/$PROJECT_NAME/
scp init.sql $USERNAME@$SERVER_IP:/root/$PROJECT_NAME/ 2>/dev/null || echo "跳过init.sql"

# 在服务器上执行部署命令
echo "🔧 在服务器上配置环境..."
ssh $USERNAME@$SERVER_IP << 'ENDSSH'
    set -e
    
    # 安装必要软件
    echo "📦 安装Docker和Docker Compose..."
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://get.docker.com | sh
        systemctl start docker
        systemctl enable docker
        usermod -aG docker root
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
    
    # 进入项目目录
    cd /root/huitong-material
    
    # 创建上传目录
    mkdir -p uploads
    
    # 设置文件权限
    chmod 755 uploads
    
    # 停止旧服务
    docker-compose down || true
    
    # 拉取最新镜像
    docker-compose pull
    
    # 启动服务
    echo "🚀 启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 30
    
    # 运行数据库迁移
    echo "🗄️ 运行数据库迁移..."
    docker-compose exec -T app npx prisma migrate deploy
    
    # 检查服务状态
    echo "🔍 检查服务状态..."
    docker-compose ps
    
    # 健康检查
    echo "🏥 健康检查..."
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        echo "✅ 服务运行正常"
    else
        echo "❌ 服务启动异常，请检查日志"
        docker-compose logs --tail=50
    fi
    
    echo "✅ 部署完成！"
ENDSSH

echo ""
echo -e "${GREEN}🎉 部署成功！${NC}"
echo "📍 应用地址: http://$SERVER_IP:3001"
echo "🔍 健康检查: http://$SERVER_IP:3001/health"
echo ""
echo "📋 后续操作:"
echo "1. 配置域名解析 (可选)"
echo "2. 配置HTTPS证书 (可选)"
echo "3. 设置定期备份"
echo ""
echo "🛠️ 常用命令:"
echo "查看日志: ssh root@$SERVER_IP 'cd /root/$PROJECT_NAME && docker-compose logs -f'"
echo "重启服务: ssh root@$SERVER_IP 'cd /root/$PROJECT_NAME && docker-compose restart'"
echo "停止服务: ssh root@$SERVER_IP 'cd /root/$PROJECT_NAME && docker-compose down'"