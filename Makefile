# 慧通材料管理系统 - 部署Makefile

.PHONY: help build up down logs clean deploy test backup

# 默认目标
help:
	@echo "慧通材料管理系统 - 部署命令"
	@echo ""
	@echo "开发命令:"
	@echo "  dev          启动开发环境"
	@echo "  build        构建Docker镜像"
	@echo "  up           启动生产环境"
	@echo "  down         停止所有服务"
	@echo "  logs         查看日志"
	@echo "  clean        清理Docker资源"
	@echo ""
	@echo "部署命令:"
	@echo "  deploy       部署到阿里云 (需要参数: IP=xxx.xxx.xxx.xxx)"
	@echo "  setup-server 初始化服务器环境"
	@echo ""
	@echo "维护命令:"
	@echo "  backup       备份数据库和上传文件"
	@echo "  restore      恢复备份"
	@echo "  update       更新应用"
	@echo ""
	@echo "测试命令:"
	@echo "  test         运行测试"
	@echo "  health       检查服务健康状态"

# 开发环境
dev:
	docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d

# 构建镜像
build:
	docker-compose build

# 启动生产环境
up:
	docker-compose up -d

# 停止服务
down:
	docker-compose down

# 查看日志
logs:
	docker-compose logs -f --tail=100

# 清理资源
clean:
	docker-compose down -v
	docker system prune -f

# 部署到阿里云
deploy:
	@if [ -z "$(IP)" ]; then \
		echo "❌ 请提供服务器IP: make deploy IP=xxx.xxx.xxx.xxx"; \
		exit 1; \
	fi
	./deploy-aliyun.sh $(IP)

# 初始化服务器
setup-server:
	@if [ -z "$(IP)" ]; then \
		echo "❌ 请提供服务器IP: make setup-server IP=xxx.xxx.xxx.xxx"; \
		exit 1; \
	fi
	ssh root@$(IP) 'bash -s' < setup-aliyun-server.sh

# 备份
backup:
	@mkdir -p backups
	@DATE=$$(date +%Y%m%d_%H%M%S); \
	docker-compose exec -T postgres pg_dump -U huitong_user huitong_material > backups/db_backup_$$DATE.sql; \
	tar -czf backups/uploads_backup_$$DATE.tar.gz uploads/; \
	echo "✅ 备份完成: backups/db_backup_$$DATE.sql 和 backups/uploads_backup_$$DATE.tar.gz"

# 恢复备份
restore:
	@if [ -z "$(FILE)" ]; then \
		echo "❌ 请提供备份文件: make restore FILE=backup_file.sql"; \
		exit 1; \
	fi
	docker-compose exec -T postgres psql -U huitong_user -d huitong_material < $(FILE)

# 更新应用
update:
	docker-compose pull
	docker-compose build --no-cache
	docker-compose up -d

# 测试
test:
	@echo "🧪 运行健康检查..."
	@curl -f http://localhost:3001/health || echo "❌ 服务未响应"

# 健康检查
health:
	@curl -s http://localhost:3001/health | jq .

# 数据库迁移
migrate:
	docker-compose exec app npx prisma migrate deploy

# 数据库重置
reset-db:
	docker-compose exec app npx prisma migrate reset --force

# 查看容器状态
status:
	docker-compose ps

# 进入容器
shell:
	docker-compose exec app sh

# 数据库控制台
db:
	docker-compose exec postgres psql -U huitong_user -d huitong_material