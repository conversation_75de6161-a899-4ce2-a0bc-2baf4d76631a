# 阿里云部署检查清单

## 部署前准备

### ✅ 服务器准备
- [ ] 已购买阿里云轻量服务器
- [ ] 已获取服务器IP地址
- [ ] 已配置SSH密钥登录
- [ ] 已开放3001端口（安全组配置）

### ✅ 域名配置（可选）
- [ ] 已购买域名
- [ ] 已配置域名解析到服务器IP
- [ ] 已申请SSL证书（Let's Encrypt或阿里云证书）

### ✅ 本地准备
- [ ] 已安装Docker和Docker Compose
- [ ] 已配置.env.production文件
- [ ] 已测试本地Docker构建

## 部署步骤

### 1. 服务器初始化
```bash
# 在服务器上执行
chmod +x setup-aliyun-server.sh
./setup-aliyun-server.sh
```

### 2. 环境配置检查
- [ ] 修改.env.production中的数据库密码
- [ ] 确认所有配置项正确

### 3. 一键部署
```bash
# Linux/Mac
chmod +x deploy-aliyun.sh
./deploy-aliyun.sh YOUR_SERVER_IP

# Windows
deploy-aliyun.bat YOUR_SERVER_IP
```

### 4. 验证部署
- [ ] 访问 http://YOUR_SERVER_IP:3001
- [ ] 检查健康状态: http://YOUR_SERVER_IP:3001/health
- [ ] 测试文件上传功能
- [ ] 测试数据库连接

## 常见问题排查

### 🔍 端口问题
```bash
# 检查端口是否开放
sudo ufw status
sudo netstat -tulnp | grep :3001
```

### 🔍 Docker问题
```bash
# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 🔍 数据库问题
```bash
# 检查数据库连接
docker-compose exec postgres psql -U huitong_user -d huitong_material -c "\dt"
```

## 安全加固

### 🔒 基础安全
- [ ] 修改默认密码
- [ ] 配置防火墙规则
- [ ] 禁用root远程登录
- [ ] 配置fail2ban

### 🔒 应用安全
- [ ] 配置HTTPS
- [ ] 设置强密码策略
- [ ] 配置CORS
- [ ] 定期更新依赖

## 性能优化

### ⚡ 系统优化
- [ ] 配置Nginx反向代理
- [ ] 启用Gzip压缩
- [ ] 配置缓存策略
- [ ] 设置监控告警

### ⚡ 数据库优化
- [ ] 配置连接池
- [ ] 设置定期备份
- [ ] 优化查询性能

## 维护计划

### 📅 日常维护
- [ ] 检查服务状态
- [ ] 查看磁盘空间
- [ ] 检查日志异常

### 📅 定期维护
- [ ] 每周备份数据
- [ ] 每月更新系统
- [ ] 每季度安全审计

## 紧急联系

### 🚨 故障处理
- 服务无法访问：检查Docker容器状态
- 数据库连接失败：检查PostgreSQL服务
- 文件上传失败：检查磁盘空间和权限

### 📞 支持渠道
- 查看DEPLOYMENT_GUIDE.md获取详细说明
- 检查项目Issues
- 联系技术支持

## 部署完成确认

### ✅ 最终检查
- [ ] 应用可正常访问
- [ ] 文件上传功能正常
- [ ] 数据库数据持久化
- [ ] 备份机制已配置
- [ ] 监控告警已启用

### 🎉 恭喜！部署完成
应用地址：http://YOUR_SERVER_IP:3001
管理面板：http://YOUR_SERVER_IP:3001/admin