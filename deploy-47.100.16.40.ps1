# PowerShell部署脚本 - 慧通材料管理系统
# 目标服务器: ************

param(
    [string]$ServerIP = "************",
    [string]$Username = "root",
    [string]$ProjectName = "huitong-material"
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "==============================================" -ForegroundColor Cyan
Write-Host "  慧通材料管理系统 - 阿里云部署脚本" -ForegroundColor Cyan
Write-Host "  目标服务器: $ServerIP" -ForegroundColor Cyan
Write-Host "==============================================" -ForegroundColor Cyan
Write-Host ""

# 检查依赖
function Test-Command {
    param([string]$Command)
    $null -ne (Get-Command $Command -ErrorAction SilentlyContinue)
}

Write-Host "检查依赖..." -ForegroundColor Yellow
$missingDeps = @()
if (!(Test-Command "docker")) { $missingDeps += "Docker" }
if (!(Test-Command "docker-compose")) { $missingDeps += "Docker Compose" }
if (!(Test-Command "ssh")) { $missingDeps += "SSH (OpenSSH)" }
if (!(Test-Command "scp")) { $missingDeps += "SCP" }

if ($missingDeps.Count -gt 0) {
    Write-Host "缺少依赖: $($missingDeps -join ', ')" -ForegroundColor Red
    Write-Host "请安装缺失的依赖后重试" -ForegroundColor Red
    exit 1
}

Write-Host "所有依赖已安装 ✓" -ForegroundColor Green

# 检查必要文件
Write-Host "检查必要文件..." -ForegroundColor Yellow
$requiredFiles = @("docker-compose.yml", ".env.production", "Dockerfile")
foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        Write-Host "文件不存在: $file" -ForegroundColor Red
        exit 1
    }
}
Write-Host "所有必要文件存在 ✓" -ForegroundColor Green

# 构建Docker镜像
Write-Host "构建Docker镜像..." -ForegroundColor Yellow
docker-compose build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Docker镜像构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "Docker镜像构建成功 ✓" -ForegroundColor Green

# 测试SSH连接
Write-Host "测试SSH连接..." -ForegroundColor Yellow
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$Username@$ServerIP" "echo 'SSH连接测试'"
if ($LASTEXITCODE -ne 0) {
    Write-Host "SSH连接失败，请检查：" -ForegroundColor Red
    Write-Host "1. 服务器是否开机" -ForegroundColor Red
    Write-Host "2. 安全组是否开放22端口" -ForegroundColor Red
    Write-Host "3. SSH密钥/密码是否正确" -ForegroundColor Red
    exit 1
}
Write-Host "SSH连接成功 ✓" -ForegroundColor Green

# 创建远程部署脚本
Write-Host "创建远程部署脚本..." -ForegroundColor Yellow
$remoteScript = @'
#!/bin/bash
set -e

echo "=============================================="
echo "  服务器端部署开始"
echo "=============================================="

cd /root/huitong-material

# 创建必要目录
echo "创建上传目录..."
mkdir -p uploads
chmod 755 uploads

# 停止旧服务
echo "停止旧服务..."
docker-compose down 2>/dev/null || true

# 启动新服务
echo "启动新服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 20

# 运行数据库迁移
echo "运行数据库迁移..."
docker-compose exec -T app npx prisma migrate deploy

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 健康检查
echo "执行健康检查..."
if curl -f http://localhost:3001/health >/dev/null 2>&1; then
    echo "健康检查通过 ✓"
else
    echo "健康检查失败 ✗"
    echo "查看日志: docker-compose logs"
    exit 1
fi

echo "=============================================="
echo "  服务器端部署完成"
echo "=============================================="
'@

# 将脚本写入临时文件
$tempScript = [System.IO.Path]::GetTempFileName() + ".sh"
$remoteScript | Out-File -FilePath $tempScript -Encoding ASCII

Write-Host "上传文件到服务器..." -ForegroundColor Yellow

# 创建远程目录
ssh "$Username@$ServerIP" "mkdir -p /root/$ProjectName"

# 上传文件
$filesToUpload = @(
    @{Local="docker-compose.yml"; Remote="/root/$ProjectName/"},
    @{Local=".env.production"; Remote="/root/$ProjectName/.env"},
    @{Local="Dockerfile"; Remote="/root/$ProjectName/"},
    @{Local="init.sql"; Remote="/root/$ProjectName/"},
    @{Local=$tempScript; Remote="/root/$ProjectName/deploy_remote.sh"}
)

foreach ($file in $filesToUpload) {
    if (Test-Path $file.Local) {
        Write-Host "  上传: $($file.Local) -> $($file.Remote)" -ForegroundColor Gray
        scp $file.Local "$Username@$ServerIP`:$($file.Remote)"
    }
}

# 设置脚本权限
ssh "$Username@$ServerIP" "chmod +x /root/$ProjectName/deploy_remote.sh"

# 执行远程部署
Write-Host "执行远程部署..." -ForegroundColor Yellow
ssh "$Username@$ServerIP" "cd /root/$ProjectName && ./deploy_remote.sh"

# 清理临时文件
Remove-Item $tempScript -ErrorAction SilentlyContinue
ssh "$Username@$ServerIP" "rm -f /root/$ProjectName/deploy_remote.sh"

Write-Host ""
Write-Host "==============================================" -ForegroundColor Green
Write-Host "  部署完成！" -ForegroundColor Green
Write-Host "==============================================" -ForegroundColor Green
Write-Host ""
Write-Host "应用地址: http://$ServerIP`:3001" -ForegroundColor Cyan
Write-Host "健康检查: http://$ServerIP`:3001/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "常用管理命令：" -ForegroundColor Yellow
Write-Host "查看日志: ssh root@$ServerIP 'cd /root/$ProjectName && docker-compose logs -f'" -ForegroundColor Gray
Write-Host "重启服务: ssh root@$ServerIP 'cd /root/$ProjectName && docker-compose restart'" -ForegroundColor Gray
Write-Host "停止服务: ssh root@$ServerIP 'cd /root/$ProjectName && docker-compose down'" -ForegroundColor Gray
Write-Host ""
Write-Host "如果需要配置域名或HTTPS，请参考 DEPLOYMENT_GUIDE.md" -ForegroundColor Cyan